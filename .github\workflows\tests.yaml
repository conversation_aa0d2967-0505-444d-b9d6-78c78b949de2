name: PaddleOCR PR Tests

on:
  push:
    branches: ["main", "release/*"]
    paths-ignore:
      - '**.md'
      - '**.txt'
      - '**.yml'
      - '**.yaml'
  pull_request:
    branches: ["main", "release/*"]
    paths-ignore:
      - '**.md'
      - '**.txt'
      - '**.yml'
      - '**.yaml'

permissions:
  contents: read

jobs:
  test-pr:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.10
      uses: actions/setup-python@v5
      with:
        python-version: "3.10"

    - name: Cache dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.cache/pip
          ~/.local/lib/python3.10/site-packages
          ~/.paddleocr/
        key: ${{ runner.os }}-dependencies-${{ hashFiles('**/requirements.txt', 'pyproject.toml') }}
        restore-keys: |
          ${{ runner.os }}-dependencies-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        python -m pip install paddlepaddle==3.0.0 -i https://www.paddlepaddle.org.cn/packages/stable/cpu/
        pip install -e .
    - name: Test with pytest
      run: |
        pytest --verbose tests/
