Global:
  debug: false
  use_gpu: true
  epoch_num: 800
  log_smooth_window: 20
  print_batch_step: 10
  save_model_dir: ./output/rec_mobile_pp-OCRv2_enhanced_ctc_loss
  save_epoch_step: 3
  eval_batch_step: [0, 2000]
  cal_metric_during_train: true
  pretrained_model:
  checkpoints:
  save_inference_dir:
  use_visualdl: false
  infer_img: doc/imgs_words/ch/word_1.jpg
  character_dict_path: ppocr/utils/ppocr_keys_v1.txt
  max_text_length: 25
  infer_mode: false
  use_space_char: true
  distributed: true
  save_res_path: ./output/rec/predicts_mobile_pp-OCRv2_enhanced_ctc_loss.txt


Optimizer:
  name: <PERSON>
  beta1: 0.9
  beta2: 0.999
  lr:
    name: Piecewise
    decay_epochs : [700]
    values : [0.001, 0.0001]
    warmup_epoch: 5
  regularizer:
    name: L2
    factor: 2.0e-05


Architecture:
  model_type: rec
  algorithm: CRNN
  Transform:
  Backbone:
    name: MobileNetV1Enhance
    scale: 0.5
  Neck:
    name: SequenceEncoder
    encoder_type: rnn
    hidden_size: 64
  Head:
    name: CTCHead
    mid_channels: 96
    fc_decay: 0.00002
    return_feats: true

Loss:
  name: CombinedLoss
  loss_config_list:
  - CTCLoss:
      use_focal_loss: false
      weight: 1.0
  - CenterLoss:
      weight: 0.05
      num_classes: 6625
      feat_dim: 96
      center_file_path:
  # you can also try to add ace loss on your own dataset
  # - ACELoss:
  #     weight: 0.1

PostProcess:
  name: CTCLabelDecode

Metric:
  name: RecMetric
  main_indicator: acc

Train:
  dataset:
    name: SimpleDataSet
    data_dir: ./train_data/
    label_file_list:
    - ./train_data/train_list.txt
    transforms:
    - DecodeImage:
        img_mode: BGR
        channel_first: false
    - RecAug:
    - CTCLabelEncode:
    - RecResizeImg:
        image_shape: [3, 32, 320]
    - KeepKeys:
        keep_keys:
        - image
        - label
        - length
        - label_ace
  loader:
    shuffle: true
    batch_size_per_card: 128
    drop_last: true
    num_workers: 8
Eval:
  dataset:
    name: SimpleDataSet
    data_dir: ./train_data
    label_file_list:
    - ./train_data/val_list.txt
    transforms:
    - DecodeImage:
        img_mode: BGR
        channel_first: false
    - CTCLabelEncode:
    - RecResizeImg:
        image_shape: [3, 32, 320]
    - KeepKeys:
        keep_keys:
        - image
        - label
        - length
  loader:
    shuffle: false
    drop_last: false
    batch_size_per_card: 128
    num_workers: 8
