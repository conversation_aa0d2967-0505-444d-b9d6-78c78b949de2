Global:
  use_gpu: True
  epoch_num: 6
  log_smooth_window: 50
  print_batch_step: 50
  save_model_dir: ./output/rec/rec_r32_gaspin_bilstm_att/
  save_epoch_step: 3
  # evaluation is run every 2000 iterations after the 4000th iteration
  eval_batch_step: [0, 2000]
  cal_metric_during_train: True
  pretrained_model:
  checkpoints:
  save_inference_dir:
  use_visualdl: False
  infer_img: doc/imgs_words_en/word_10.png
  # for data or label process
  character_dict_path: ./ppocr/utils/dict/spin_dict.txt
  max_text_length: 25
  infer_mode: False
  use_space_char: False
  save_res_path: ./output/rec/predicts_r32_gaspin_bilstm_att.txt


Optimizer:
  name: AdamW
  beta1: 0.9
  beta2: 0.999
  lr:
    name: Piecewise
    decay_epochs: [3, 4, 5]
    values: [0.001, 0.0003, 0.00009, 0.000027] 
  clip_norm: 5

Architecture:
  model_type: rec
  algorithm: SPIN
  in_channels: 1
  Transform:
    name: GA_SPIN
    offsets: True
    default_type: 6
    loc_lr: 0.1
    stn: True
  Backbone:
    name: ResNet32
    out_channels: 512
  Neck:
    name: SequenceEncoder
    encoder_type: cascadernn 
    hidden_size: 256
    out_channels: [256, 512]
    with_linear: True
  Head:
    name: SPINAttentionHead  
    hidden_size: 256
    

Loss:
  name: SPINAttentionLoss
  ignore_index: 0

PostProcess:
  name: SPINLabelDecode
  use_space_char: False


Metric:
  name: RecMetric
  main_indicator: acc
  is_filter: True

Train:
  dataset:
    name: SimpleDataSet
    data_dir: ./train_data/ic15_data/
    label_file_list: ["./train_data/ic15_data/rec_gt_train.txt"]
    transforms:
      - DecodeImage: # load image
          img_mode: BGR
          channel_first: False
      - SPINLabelEncode: # Class handling label
      - SPINRecResizeImg:
          image_shape: [100, 32]
          interpolation : 2
          mean: [127.5]
          std: [127.5]
      - KeepKeys:
          keep_keys: ['image', 'label', 'length'] # dataloader will return list in this order
  loader:
    shuffle: True
    batch_size_per_card: 8
    drop_last: True
    num_workers: 4

Eval:
  dataset:
    name: SimpleDataSet
    data_dir: ./train_data/ic15_data
    label_file_list: ["./train_data/ic15_data/rec_gt_test.txt"]
    transforms:
      - DecodeImage: # load image
          img_mode: BGR
          channel_first: False
      - SPINLabelEncode: # Class handling label
      - SPINRecResizeImg:
          image_shape: [100, 32]
          interpolation : 2
          mean: [127.5]
          std: [127.5]
      - KeepKeys:
          keep_keys: ['image', 'label', 'length'] # dataloader will return list in this order
  loader:
    shuffle: False
    drop_last: False
    batch_size_per_card: 8
    num_workers: 2
