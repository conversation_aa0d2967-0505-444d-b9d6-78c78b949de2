Global:
  use_gpu: true
  epoch_num: 1200
  log_smooth_window: 20
  print_batch_step: 5
  save_model_dir: ./output/det_r50_drrg_ctw/
  save_epoch_step: 100
  # evaluation is run every 1260 iterations
  eval_batch_step: [37800, 1260]
  cal_metric_during_train: False
  pretrained_model: ./pretrain_models/ResNet50_vd_ssld_pretrained.pdparams 
  checkpoints: 
  save_inference_dir: 
  use_visualdl: False
  infer_img: doc/imgs_en/img_10.jpg
  save_res_path: ./output/det_drrg/predicts_drrg.txt


Architecture:
  model_type: det
  algorithm: DRRG
  Transform:
  Backbone:
    name: ResNet_vd
    layers: 50
  Neck:
    name: FPN_UNet
    in_channels: [256, 512, 1024, 2048]
    out_channels: 32
  Head:
    name: DRRGHead
    in_channels: 32
    text_region_thr: 0.3
    center_region_thr: 0.4
Loss:
  name: DRRGLoss
  
Optimizer:
  name: Momentum
  momentum: 0.9
  lr:
    name: DecayLearningRate
    learning_rate: 0.028
    epochs: 1200
    factor: 0.9
    end_lr: 0.0000001
  weight_decay: 0.0001

PostProcess:
  name: DRRGPostprocess
  link_thr: 0.8

Metric:
  name: DetFCEMetric
  main_indicator: hmean

Train:
  dataset:
    name: SimpleDataSet
    data_dir: ./train_data/ctw1500/imgs/
    label_file_list: 
      - ./train_data/ctw1500/imgs/training.txt
    transforms:
      - DecodeImage: # load image
          img_mode: BGR
          channel_first: False
          ignore_orientation: True
      - DetLabelEncode: # Class handling label
      - ColorJitter: 
          brightness: 0.12549019607843137
          saturation: 0.5
      - RandomScaling: 
      - RandomCropFlip:
          crop_ratio: 0.5
      - RandomCropPolyInstances:
          crop_ratio: 0.8
          min_side_ratio: 0.3
      - RandomRotatePolyInstances:
          rotate_ratio: 0.5
          max_angle: 60
          pad_with_fixed_color: False
      - SquareResizePad:
          target_size: 800
          pad_ratio: 0.6
      - IaaAugment:
          augmenter_args:
            - { 'type': Fliplr, 'args': { 'p': 0.5 } }
      - DRRGTargets:
      - NormalizeImage:
          scale: 1./255.
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          order: 'hwc'
      - ToCHWImage:
      - KeepKeys:
          keep_keys: ['image', 'gt_text_mask', 'gt_center_region_mask', 'gt_mask',
            'gt_top_height_map', 'gt_bot_height_map', 'gt_sin_map',
            'gt_cos_map', 'gt_comp_attribs'] # dataloader will return list in this order
  loader:
    shuffle: True
    drop_last: False
    batch_size_per_card: 4
    num_workers: 8

Eval:
  dataset:
    name: SimpleDataSet
    data_dir: ./train_data/ctw1500/imgs/
    label_file_list:
      - ./train_data/ctw1500/imgs/test.txt
    transforms:
      - DecodeImage: # load image
          img_mode: BGR
          channel_first: False
          ignore_orientation: True
      - DetLabelEncode: # Class handling label
      - DetResizeForTest:
          limit_type: 'min'
          limit_side_len: 640
      - NormalizeImage:
          scale: 1./255.
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          order: 'hwc'
      - Pad: 
      - ToCHWImage:
      - KeepKeys:
          keep_keys: ['image', 'shape', 'polys', 'ignore_tags']
  loader:
    shuffle: False
    drop_last: False
    batch_size_per_card: 1 # must be 1
    num_workers: 2
