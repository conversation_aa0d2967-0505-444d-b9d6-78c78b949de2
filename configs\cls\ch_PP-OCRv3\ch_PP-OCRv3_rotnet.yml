Global:
  debug: false
  use_gpu: true
  epoch_num: 100
  log_smooth_window: 20
  print_batch_step: 10
  save_model_dir: ./output/rec_ppocr_v3_rotnet
  save_epoch_step: 3
  eval_batch_step: [0, 2000]
  cal_metric_during_train: true
  pretrained_model: null
  checkpoints: null
  save_inference_dir: null
  use_visualdl: false
  infer_img: doc/imgs_words/ch/word_1.jpg
  character_dict_path: ppocr/utils/ppocr_keys_v1.txt
  max_text_length: 25
  infer_mode: false
  use_space_char: true
  save_res_path: ./output/rec/predicts_chinese_lite_v2.0.txt
Optimizer:
  name: <PERSON>
  beta1: 0.9
  beta2: 0.999
  lr:
    name: Cosine
    learning_rate: 0.001
  regularizer:
    name: L2
    factor: 1.0e-05
Architecture:
  model_type: cls
  algorithm: CLS
  Transform: null
  Backbone:
    name: MobileNetV1Enhance
    scale: 0.5
    last_conv_stride: [1, 2]
    last_pool_type: avg
  Neck:
  Head:
    name: ClsHead
    class_dim: 4

Loss:
  name: ClsLoss
  main_indicator: acc

PostProcess:
  name: ClsPostProcess

Metric:
  name: ClsMetric
  main_indicator: acc

Train:
  dataset:
    name: SimpleDataSet
    data_dir: ./train_data
    label_file_list:
    - ./train_data/train_list.txt
    transforms:
    - DecodeImage:
        img_mode: BGR
        channel_first: false
    - BaseDataAugmentation:
    - RandAugment:
    - SSLRotateResize:
        image_shape: [3, 48, 320]
    - KeepKeys:
        keep_keys: ["image", "label"]
  loader:
    collate_fn: "SSLRotateCollate"
    shuffle: true
    batch_size_per_card: 32
    drop_last: true
    num_workers: 8
Eval:
  dataset:
    name: SimpleDataSet
    data_dir: ./train_data
    label_file_list:
    - ./train_data/val_list.txt
    transforms:
    - DecodeImage:
        img_mode: BGR
        channel_first: false
    - SSLRotateResize:
        image_shape: [3, 48, 320]
    - KeepKeys:
        keep_keys: ["image", "label"]
  loader:
    collate_fn: "SSLRotateCollate"
    shuffle: false
    drop_last: false
    batch_size_per_card: 64
    num_workers: 8
profiler_options: null
