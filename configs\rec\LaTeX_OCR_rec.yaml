Global:
  model_name: LaTeX_OCR_rec # To use static model for inference.
  use_gpu: True
  epoch_num: 500
  log_smooth_window: 20
  print_batch_step: 100
  save_model_dir: ./output/rec/latex_ocr/
  save_epoch_step: 5
  max_seq_len: 512
  # evaluation is run every 60000 iterations (22 epoch)(batch_size = 56)
  eval_batch_step: [0, 60000]
  cal_metric_during_train: True
  pretrained_model:
  checkpoints:
  save_inference_dir:
  use_visualdl: False
  infer_img: doc/datasets/pme_demo/0000013.png
  infer_mode: False
  use_space_char: False
  rec_char_dict_path:  ppocr/utils/dict/latex_ocr_tokenizer.json
  save_res_path: ./output/rec/predicts_latexocr.txt
  d2s_train_image_shape: [1,256,256]
  find_unused_parameters: True

Optimizer:
  name: AdamW
  beta1: 0.9
  beta2: 0.999
  lr:
    name: Const
    learning_rate: 0.0001

Architecture:
  model_type: rec
  algorithm: LaTeXOCR
  in_channels: 1
  Transform:
  Backbone:
    name: HybridTransformer
    img_size: [192, 672]
    patch_size: 16
    num_classes: 0
    embed_dim: 256
    depth: 4
    num_heads: 8
    input_channel: 1
    is_predict: False
    is_export: False
  Head:
    name: LaTeXOCRHead
    pad_value: 0
    is_export: False
    decoder_args:
      attn_on_attn: True
      cross_attend: True
      ff_glu: True
      rel_pos_bias: False
      use_scalenorm: False

Loss:
  name: LaTeXOCRLoss

PostProcess:
  name: LaTeXOCRDecode
  rec_char_dict_path: ppocr/utils/dict/latex_ocr_tokenizer.json

Metric:
  name: LaTeXOCRMetric
  main_indicator:  exp_rate
  cal_bleu_score: True

Train:
  dataset:
    name: LaTeXOCRDataSet
    data_dir: ./train_data/LaTeXOCR/train
    data: ./train_data/LaTeXOCR/latexocr_train.pkl
    min_dimensions: [32, 32]
    max_dimensions: [672, 192]
    batch_size_per_pair: 56
    keep_smaller_batches: False
    transforms:
      - DecodeImage:
          channel_first: False
      - MinMaxResize:
          min_dimensions: [32, 32]
          max_dimensions: [672, 192]        
      - LatexTrainTransform:
          bitmap_prob: .04
      - NormalizeImage:
          mean: [0.7931, 0.7931, 0.7931]
          std: [0.1738, 0.1738, 0.1738]
          order: 'hwc'
      - LatexImageFormat:
      - KeepKeys:
          keep_keys: ['image']
  loader:
    shuffle: True
    batch_size_per_card: 1
    drop_last: False
    num_workers: 0
    collate_fn: LaTeXOCRCollator

Eval:
  dataset:
    name: LaTeXOCRDataSet
    data_dir: ./train_data/LaTeXOCR/val
    data: ./train_data/LaTeXOCR/latexocr_val.pkl
    min_dimensions: [32, 32]
    max_dimensions: [672, 192]
    batch_size_per_pair: 10
    keep_smaller_batches: True
    transforms:
      - DecodeImage:
          channel_first: False
      - MinMaxResize:
          min_dimensions: [32, 32]
          max_dimensions: [672, 192]  
      - LatexTestTransform:
      - NormalizeImage:
          mean: [0.7931, 0.7931, 0.7931]
          std: [0.1738, 0.1738, 0.1738]
          order: 'hwc'
      - LatexImageFormat:
      - KeepKeys:
          keep_keys: ['image']
  loader:
    shuffle: False
    drop_last: False
    batch_size_per_card: 1
    num_workers: 0
    collate_fn: LaTeXOCRCollator
